// Service recipes implements recipe management for the food planning application.
package recipes

import (
	"context"
	"fmt"
	"net/http"
)

// Recipe represents a recipe in the system
type Recipe struct {
	ID          int    `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	ImageURL    string `json:"image_url"`
	PrepTime    string `json:"prep_time"`
	Difficulty  string `json:"difficulty"`
}

// FeaturedRecipesResponse returns HTML for featured recipes
type FeaturedRecipesResponse struct {
	Content string `json:"content"`
}

// GetFeaturedRecipes returns HTML content for featured recipes (for HTMX)
//
//encore:api public method=GET path=/api/recipes/featured
func GetFeaturedRecipes(ctx context.Context, w http.ResponseWriter, r *http.Request) error {
	// Sample featured recipes data
	featuredRecipes := []Recipe{
		{
			ID:          1,
			Title:       "Classic Spaghetti Carbonara",
			Description: "A traditional Italian pasta dish with eggs, cheese, and pancetta",
			ImageURL:    "https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400",
			PrepTime:    "20 mins",
			Difficulty:  "Medium",
		},
		{
			ID:          2,
			Title:       "Chicken Tikka Masala",
			Description: "Creamy and flavorful Indian curry with tender chicken pieces",
			ImageURL:    "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400",
			PrepTime:    "45 mins",
			Difficulty:  "Medium",
		},
		{
			ID:          3,
			Title:       "Fresh Garden Salad",
			Description: "Crisp vegetables with homemade vinaigrette dressing",
			ImageURL:    "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400",
			PrepTime:    "10 mins",
			Difficulty:  "Easy",
		},
	}

	// Generate HTML for the recipes
	html := ""
	for _, recipe := range featuredRecipes {
		html += fmt.Sprintf(`
		<div class="col-md-4 mb-4">
			<div class="card h-100 shadow-sm">
				<img src="%s" class="card-img-top" alt="%s" style="height: 200px; object-fit: cover;">
				<div class="card-body d-flex flex-column">
					<h5 class="card-title">%s</h5>
					<p class="card-text flex-grow-1">%s</p>
					<div class="d-flex justify-content-between align-items-center mt-auto">
						<small class="text-muted">
							<i class="fas fa-clock me-1"></i>%s
						</small>
						<span class="badge bg-primary">%s</span>
					</div>
					<button class="btn btn-outline-primary mt-2"
							hx-get="/api/recipes/%d"
							hx-target="#recipe-modal-content"
							data-bs-toggle="modal"
							data-bs-target="#recipeModal">
						View Recipe
					</button>
				</div>
			</div>
		</div>`,
			recipe.ImageURL,
			recipe.Title,
			recipe.Title,
			recipe.Description,
			recipe.PrepTime,
			recipe.Difficulty,
			recipe.ID,
		)
	}

	return &FeaturedRecipesResponse{Content: html}, nil
}

// GetRecipe returns detailed recipe information
//
//encore:api public method=GET path=/api/recipes/:id
func GetRecipe(ctx context.Context, id int) (*Recipe, error) {
	// Sample recipe data - in a real app, this would come from a database
	recipes := map[int]Recipe{
		1: {
			ID:          1,
			Title:       "Classic Spaghetti Carbonara",
			Description: "A traditional Italian pasta dish with eggs, cheese, and pancetta. This authentic recipe creates a creamy sauce without using cream, relying on the technique of combining hot pasta with eggs and cheese.",
			ImageURL:    "https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=600",
			PrepTime:    "20 mins",
			Difficulty:  "Medium",
		},
		2: {
			ID:          2,
			Title:       "Chicken Tikka Masala",
			Description: "Creamy and flavorful Indian curry with tender chicken pieces marinated in yogurt and spices, then simmered in a rich tomato-based sauce.",
			ImageURL:    "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=600",
			PrepTime:    "45 mins",
			Difficulty:  "Medium",
		},
		3: {
			ID:          3,
			Title:       "Fresh Garden Salad",
			Description: "Crisp vegetables with homemade vinaigrette dressing. Perfect as a side dish or light meal, featuring seasonal vegetables and herbs.",
			ImageURL:    "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=600",
			PrepTime:    "10 mins",
			Difficulty:  "Easy",
		},
	}

	recipe, exists := recipes[id]
	if !exists {
		return nil, fmt.Errorf("recipe with id %d not found", id)
	}

	return &recipe, nil
}
