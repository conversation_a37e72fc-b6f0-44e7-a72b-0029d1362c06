// Service web implements HTML pages for the food planning application.
package web

import (
	"context"
	"html/template"
	"net/http"
)

// Home page endpoint that returns HTML with Bootstrap and HTMX
//
//encore:api public method=GET path=/
func Home(ctx context.Context) (*HTMLResponse, error) {
	html := `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food Planner - Home</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-D1Kt99CQMDuVetoL1lrYwg5t+9QdHe7NLX/SoJYkXDFfX37iInKRy5xLSi8nO7UC" crossorigin="anonymous"></script>
    
    <!-- Custom styles -->
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-utensils me-2"></i>Food Planner
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/recipes">Recipes</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/groups">Groups</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">Welcome to Food Planner</h1>
            <p class="lead mb-4">Organize your recipes, plan your meals, and discover new culinary adventures</p>
            <button class="btn btn-light btn-lg" hx-get="/api/recipes/featured" hx-target="#featured-recipes">
                Explore Featured Recipes
            </button>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Features Section -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h2 class="h3 fw-bold">What You Can Do</h2>
                <p class="text-muted">Discover the power of organized meal planning</p>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-download fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">Download Recipes</h5>
                        <p class="card-text">Easily import and save recipes from your favorite cooking websites and blogs.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-layer-group fa-3x text-success"></i>
                        </div>
                        <h5 class="card-title">Organize Groups</h5>
                        <p class="card-text">Create custom groups to categorize your recipes by cuisine, diet, or occasion.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-calendar-alt fa-3x text-warning"></i>
                        </div>
                        <h5 class="card-title">Plan Meals</h5>
                        <p class="card-text">Schedule your weekly meals and generate shopping lists automatically.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Recipes Section -->
        <div class="row">
            <div class="col-12">
                <h3 class="mb-4">Featured Recipes</h3>
                <div id="featured-recipes" class="row">
                    <!-- HTMX will load content here -->
                    <div class="col-12 text-center py-5">
                        <p class="text-muted">Click "Explore Featured Recipes" to see our recommendations!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Food Planner. Built with Encore, Bootstrap, and HTMX.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <!-- Font Awesome for icons -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>`

	return &HTMLResponse{
		Content: html,
	}, nil
}

// HTMLResponse represents an HTML response
type HTMLResponse struct {
	Content string
}

// ServeHTTP implements the http.Handler interface to serve HTML content
func (r *HTMLResponse) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(r.Content))
}
